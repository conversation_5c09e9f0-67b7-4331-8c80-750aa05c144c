import 'package:flutter/material.dart';
import 'package:get/get_instance/src/bindings_interface.dart';

class GetxPageWrapper extends StatelessWidget {
  final Bindings binding;
  final Widget child;

  const GetxPageWrapper({
    super.key,
    required this.binding,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    // 在构建 child 之前，先执行依赖注入
    binding.dependencies();
    return child;
  }
}
