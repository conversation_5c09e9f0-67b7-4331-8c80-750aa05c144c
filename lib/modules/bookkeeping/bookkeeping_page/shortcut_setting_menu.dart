import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_page/bookkeeping_controller.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_page/shortcut_setting_controller.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';

class ShortcutSettingMenu extends GetView<ShortcutSettingController> {
  const ShortcutSettingMenu({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // 标题栏
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border: Border(bottom: BorderSide(color: Color(0xFFF5F5F5))),
            ),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: const Icon(Icons.close, color: MColor.xFF999999),
                ),
                const Expanded(
                  child: Text(
                    '设置横滑组件',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: MColor.xFF1B1C1A,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    // onReorder(items);
                    Navigator.pop(context);
                  },
                  child: const Text(
                    '完成',
                    style: TextStyle(
                      fontSize: 16,
                      color: MColor.skin,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // 提示文字
          const Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              '长按拖动可以调整顺序',
              style: TextStyle(
                fontSize: 14,
                color: MColor.xFF999999,
              ),
            ),
          ),
          // 可拖动列表
          // Expanded(
          //   child: ReorderableListView.builder(
          //     padding: const EdgeInsets.symmetric(horizontal: 16),
          //     itemCount: items.length,
          //     onReorder: (oldIndex, newIndex) {
          //       if (newIndex > oldIndex) {
          //         newIndex -= 1;
          //       }
          //       final item = items.removeAt(oldIndex);
          //       items.insert(newIndex, item);
          //     },
          //     itemBuilder: (context, index) {
          //       final item = items[index];
          //       return Container(
          //         key: ValueKey(item),
          //         margin: const EdgeInsets.only(bottom: 8),
          //         padding: const EdgeInsets.all(16),
          //         decoration: BoxDecoration(
          //           color: const Color(0xFFF8F8F8),
          //           borderRadius: BorderRadius.circular(8),
          //         ),
          //         child: Row(
          //           children: [
          //             Icon(
          //               _getItemIcon(item),
          //               color: MColor.skin,
          //               size: 20,
          //             ),
          //             const SizedBox(width: 12),
          //             Expanded(
          //               child: Text(
          //                 _getItemName(item),
          //                 style: const TextStyle(
          //                   fontSize: 16,
          //                   color: MColor.xFF1B1C1A,
          //                 ),
          //               ),
          //             ),
          //             const Icon(
          //               Icons.drag_handle,
          //               color: MColor.xFF999999,
          //             ),
          //           ],
          //         ),
          //       );
          //     },
          //   ),
          // ),
        ],
      ),
    );
  }
}

// class _AccountSelectionWidget extends StatefulWidget {
//   final int selectedTab;
//   final AccountModel? lastAccount;
//   final List<AccountModel> shortcutAccounts;
//   final List<AccountModel> allAccounts;
//   final Function(AccountModel) onAccountSelected;
//   final Function(AccountModel) onSetShortcut;
//   final Function(AccountModel) onCancelShortcut;
//   final Function() onRefreshData;

//   const _AccountSelectionWidget({
//     required this.selectedTab,
//     this.lastAccount,
//     required this.shortcutAccounts,
//     required this.allAccounts,
//     required this.onAccountSelected,
//     required this.onSetShortcut,
//     required this.onCancelShortcut,
//     required this.onRefreshData,
//   });

//   @override
//   State<_AccountSelectionWidget> createState() => _AccountSelectionWidgetState();
// }

// class _AccountSelectionWidgetState extends State<_AccountSelectionWidget> {
//   bool _isManageMode = false;
//   List<AccountModel> _reorderableShortcuts = [];

//   @override
//   void initState() {
//     super.initState();
//     _reorderableShortcuts = List.from(widget.shortcutAccounts);
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         // 标题栏
//         Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             Text(
//               widget.selectedTab == 0 ? '支出账户' : '收入账户',
//               style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: MColor.xFF1B1C1A),
//             ),
//             if (widget.shortcutAccounts.isNotEmpty)
//               GestureDetector(
//                 onTap: () {
//                   setState(() {
//                     _isManageMode = !_isManageMode;
//                   });
//                 },
//                 child: Text(
//                   _isManageMode ? '完成' : '管理',
//                   style: const TextStyle(fontSize: 14, color: MColor.skin),
//                 ),
//               ),
//           ],
//         ),
//         const SizedBox(height: 16),

//         if (_isManageMode) ...[
//           // 管理模式：只显示快捷账户，可拖动排序
//           _buildManageMode(),
//         ] else ...[
//           // 普通模式：显示所有账户
//           _buildNormalMode(),
//         ],
//       ],
//     );
//   }

//   Widget _buildManageMode() {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         const Text(
//           '拖动排序快捷账户',
//           style: TextStyle(fontSize: 14, color: MColor.xFF999999),
//         ),
//         const SizedBox(height: 12),
//         ReorderableListView.builder(
//           shrinkWrap: true,
//           physics: const NeverScrollableScrollPhysics(),
//           itemCount: _reorderableShortcuts.length,
//           onReorder: (oldIndex, newIndex) {
//             setState(() {
//               if (newIndex > oldIndex) {
//                 newIndex -= 1;
//               }
//               final item = _reorderableShortcuts.removeAt(oldIndex);
//               _reorderableShortcuts.insert(newIndex, item);
//             });
//             _saveShortcutOrder();
//           },
//           itemBuilder: (context, index) {
//             final account = _reorderableShortcuts[index];
//             return _buildAccountTile(
//               account,
//               key: ValueKey(account.id),
//               showTrailing: true,
//               trailingWidget: const Icon(Icons.drag_handle, color: MColor.xFF999999),
//             );
//           },
//         ),
//       ],
//     );
//   }

//   Widget _buildNormalMode() {
//     List<Widget> children = [];

//     // 最后选择的账户
//     if (widget.lastAccount != null) {
//       children.add(_buildSectionTitle('最近使用'));
//       children.add(_buildAccountTile(widget.lastAccount!, isLast: true));
//       children.add(const SizedBox(height: 16));
//     }

//     // 快捷账户
//     if (widget.shortcutAccounts.isNotEmpty) {
//       children.add(_buildSectionTitle('快捷账户'));
//       for (var account in widget.shortcutAccounts) {
//         if (widget.lastAccount == null || account.id != widget.lastAccount!.id) {
//           children.add(_buildAccountTile(account, isShortcut: true));
//         }
//       }
//       children.add(const SizedBox(height: 16));
//     }

//     // 分割线
//     children.add(const Divider(color: MColor.xFFEEEEEE, height: 1));
//     children.add(const SizedBox(height: 16));

//     // 其他账户
//     children.add(_buildSectionTitle('其他账户'));
//     final otherAccounts = widget.allAccounts.where((account) {
//       final isLast = widget.lastAccount?.id == account.id;
//       final isShortcut = widget.shortcutAccounts.any((shortcut) => shortcut.id == account.id);
//       return !isLast && !isShortcut;
//     }).toList();

//     for (var account in otherAccounts) {
//       children.add(_buildAccountTile(account));
//     }

//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: children,
//     );
//   }

//   Widget _buildSectionTitle(String title) {
//     return Padding(
//       padding: const EdgeInsets.only(bottom: 8),
//       child: Text(
//         title,
//         style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: MColor.xFF999999),
//       ),
//     );
//   }

//   Widget _buildAccountTile(
//     AccountModel account, {
//     Key? key,
//     bool isLast = false,
//     bool isShortcut = false,
//     bool showTrailing = false,
//     Widget? trailingWidget,
//   }) {
//     final isInShortcuts = widget.shortcutAccounts.any((shortcut) => shortcut.id == account.id);

//     return Container(
//       key: key,
//       margin: const EdgeInsets.only(bottom: 8),
//       child: ListTile(
//         contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
//         leading: Image.asset('assets/images/ic_card.png', width: 24, height: 24),
//         title: Text(
//           account.accountName ?? '账户',
//           style: const TextStyle(fontSize: 14, color: MColor.xFF1B1C1A),
//         ),
//         trailing: showTrailing
//             ? trailingWidget
//             : isInShortcuts
//                 ? GestureDetector(
//                     onTap: () => widget.onCancelShortcut(account),
//                     child: Container(
//                       padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
//                       decoration: BoxDecoration(
//                         color: MColor.xFFFF7858,
//                         borderRadius: BorderRadius.circular(12),
//                       ),
//                       child: const Text(
//                         '取消快捷',
//                         style: TextStyle(fontSize: 12, color: MColor.xFFFFFFFF),
//                       ),
//                     ),
//                   )
//                 : GestureDetector(
//                     onTap: () => widget.onSetShortcut(account),
//                     child: Container(
//                       padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
//                       decoration: BoxDecoration(
//                         color: MColor.skin,
//                         borderRadius: BorderRadius.circular(12),
//                       ),
//                       child: const Text(
//                         '添加快捷',
//                         style: TextStyle(fontSize: 12, color: MColor.xFFFFFFFF),
//                       ),
//                     ),
//                   ),
//         onTap: () {
//           widget.onAccountSelected(account);
//           RouterHelper.router.pop();
//         },
//       ),
//     );
//   }

//   Future<void> _saveShortcutOrder() async {
//     try {
//       final accountIds = _reorderableShortcuts.map((account) => account.id.toString()).toList();
//       await BookkeepingRepo.sortShortcutAccounts(
//         type: widget.selectedTab == 0 ? 1 : 2,
//         accounts: accountIds,
//       );
//       widget.onRefreshData();
//     } catch (e) {
//       showToast('保存排序失败: $e');
//     }
//   }
// }
